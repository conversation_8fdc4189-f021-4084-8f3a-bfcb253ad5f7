const mongoose = require('mongoose');

// Sub-schema for Car Reviews
const reviewSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // Reference to User model
  comment: { type: String, required: true },
  rating: { type: Number, min: 1, max: 5, required: true },
  createdAt: { type: Date, default: Date.now }
});

// Main Car schema
const carSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  photos: [{ type: String }], // Array of image URLs
  features: [{ type: String }], // List of features like mileage, engine, etc.
  exShowroomPrice: { type: Number, required: true },
  reviews: [reviewSchema], // Embedded Reviews subdocuments
}, { timestamps: true });

const Car = mongoose.model('Car', carSchema);

module.exports = Car;
