const express = require('express');
const router = express.Router();
const Car = require('../models/Car');
const { authenticateToken, authorizeRoles } = require('../middleware/authMiddleware');

// Add new car - Admin only
router.post('/', authenticateToken, authorizeRoles(['admin']), async (req, res) => {
  try {
    const { name, photos, features, exShowroomPrice } = req.body;
    const carExists = await Car.findOne({ name });
    if (carExists) return res.status(400).json({ error: 'Car with this name already exists' });

    const newCar = new Car({ name, photos, features, exShowroomPrice });
    await newCar.save();
    res.status(201).json({ message: 'Car added', car: newCar });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Get all cars - Public
router.get('/', async (req, res) => {
  try {
    const cars = await Car.find();
    res.json(cars);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get car by ID - Public
router.get('/:id', async (req, res) => {
  try {
    const car = await Car.findById(req.params.id);
    if (!car) return res.status(404).json({ error: 'Car not found' });
    res.json(car);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Update car by ID - Admin only
router.put('/:id', authenticateToken, authorizeRoles(['admin']), async (req, res) => {
  try {
    const updatedCar = await Car.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedCar) return res.status(404).json({ error: 'Car not found' });
    res.json(updatedCar);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Delete car by ID - Admin only
router.delete('/:id', authenticateToken, authorizeRoles(['admin']), async (req, res) => {
  try {
    const deletedCar = await Car.findByIdAndDelete(req.params.id);
    if (!deletedCar) return res.status(404).json({ error: 'Car not found' });
    res.json({ message: 'Car deleted' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
