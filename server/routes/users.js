const express = require('express');
const router = express.Router();
const User = require('../models/User');
// Import authentication and authorization middleware
const { authenticateToken, authorizeRoles } = require('../middleware/authMiddleware');

// Create User (POST)
router.post('/create', async (req, res) => {
  try {
    const { username, email, password, role } = req.body;
    const newUser = new User({ username, email, password, role });
    await newUser.save();
    res.status(201).json({ message: 'User created', user: newUser });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Read all users (GET)
router.get('/', async (req, res) => {
  try {
    const users = await User.find();
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update user by ID (PUT)
router.put('/:id', authenticateToken, authorizeRoles(['admin', 'user']), async (req, res) => {
  try {
    const userIdFromToken = req.user.id;  // set in authMiddleware 
    const paramUserId = req.params.id;

    if (userIdFromToken !== paramUserId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Forbidden: cannot update other user' });
    }

    const updatedUser = await User.findByIdAndUpdate(
      paramUserId,
      req.body,
      { new: true }
    );
    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }
    res.json(updatedUser);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Delete user by ID (DELETE)
router.delete('/:id', authenticateToken, authorizeRoles(['admin']), async (req, res) => {
  try {
    const deleted = await User.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: 'User not found' });
    res.json({ message: 'User deleted' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;
