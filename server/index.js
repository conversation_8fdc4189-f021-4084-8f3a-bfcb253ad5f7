const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Add this line to import user routes
const userRoutes = require('./routes/users');

// Add this line to import auth routes
const authRoutes = require('./routes/auth');
app.use('/api/auth', authRoutes);

// Add this line to import car routes
const carRoutes = require('./routes/cars');
app.use('/api/cars', carRoutes);

// Middleware to parse JSON bodies
app.use(express.json());

// Use the user routes; all user-related APIs will start with /api/users
app.use('/api/users', userRoutes);

// Basic error-handling middleware (handles errors from routes)
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Connect to MongoDB using Mongoose and environment variable
mongoose.connect(process.env.MONGODB_URI)
.then(() => console.log('MongoDB connected successfully'))
.catch((err) => console.error('MongoDB connection error:', err));


// Basic route for testing
app.get('/', (req, res) => {
  res.send('Hello World from your Express server connected to MongoDB!');
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
