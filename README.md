# Car Website - Full Stack Application

A modern car dealership website built with React TypeScript frontend and Node.js Express backend.

## 🚀 Project Structure

```
car-website/
├── client/          # React TypeScript Frontend (Vite)
├── server/          # Node.js Express Backend
├── MD/              # Documentation
├── .gitignore       # Git ignore rules
└── README.md        # This file
```

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software

1. **Node.js** (v18.0.0 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** (comes with Node.js) or **yarn**
   - Verify npm: `npm --version`
   - Or install yarn: `npm install -g yarn`

3. **MongoDB**
   - **Option A**: MongoDB Atlas (Cloud) - https://www.mongodb.com/atlas
   - **Option B**: Local MongoDB - https://www.mongodb.com/try/download/community
   - Verify installation: `mongod --version`

4. **Git**
   - Download from: https://git-scm.com/
   - Verify installation: `git --version`

### Optional but Recommended

- **MongoDB Compass** (GUI for MongoDB): https://www.mongodb.com/products/compass
- **Postman** (API testing): https://www.postman.com/
- **VS Code** (Code editor): https://code.visualstudio.com/

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd car-website
```

### 2. Backend Setup (Server)

```bash
# Navigate to server directory
cd server

# Install dependencies
npm install

# Create environment file
cp .env.example .env
# OR create .env manually with the following variables:
```

Create a `.env` file in the `server` directory with:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/car-website
# OR for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/car-website

# JWT Secret (generate a secure random string)
JWT_SECRET=your-super-secure-jwt-secret-key-here

# Server Configuration
PORT=5000
NODE_ENV=development

# Optional: Email configuration (if implementing email features)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-app-password
```

### 3. Frontend Setup (Client)

```bash
# Navigate to client directory (from project root)
cd client

# Install dependencies
npm install
```

### 4. Database Setup

#### Option A: MongoDB Atlas (Cloud)
1. Create account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Get connection string and update `MONGODB_URI` in server `.env`

#### Option B: Local MongoDB
1. Install MongoDB Community Edition
2. Start MongoDB service:
   ```bash
   # On macOS (with Homebrew)
   brew services start mongodb/brew/mongodb-community
   
   # On Windows
   net start MongoDB
   
   # On Linux
   sudo systemctl start mongod
   ```

## 🚀 Running the Application

### Development Mode

You'll need to run both frontend and backend simultaneously.

#### Terminal 1 - Backend Server
```bash
cd server
npm run dev
# OR if you don't have a dev script:
node index.js
```
Server will run on: http://localhost:5000

#### Terminal 2 - Frontend Client
```bash
cd client
npm run dev
```
Client will run on: http://localhost:5173

### Production Build

#### Build Frontend
```bash
cd client
npm run build
```

#### Start Production Server
```bash
cd server
npm start
```

## 🔧 Available Scripts

### Server Scripts
```bash
cd server
npm start          # Start production server
npm run dev        # Start development server with nodemon
npm test           # Run tests
```

### Client Scripts
```bash
cd client
npm run dev        # Start development server
npm run build      # Build for production
npm run preview    # Preview production build
npm run lint       # Run ESLint
```

## 📁 Project Features

### Frontend (React TypeScript)
- ⚛️ React 19 with TypeScript
- 🚀 Vite for fast development
- 🎨 Modern CSS styling
- 🔐 Authentication context
- 📱 Responsive design
- 🛣️ React Router for navigation

### Backend (Node.js Express)
- 🚀 Express.js server
- 🔐 JWT authentication
- 🔒 bcrypt password hashing
- 📊 MongoDB with Mongoose
- 🛡️ Security middleware
- 📝 RESTful API design

## 🔐 Environment Variables

### Server (.env)
```env
MONGODB_URI=mongodb://localhost:27017/car-website
JWT_SECRET=your-jwt-secret
PORT=5000
NODE_ENV=development
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License.

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 5000
   lsof -ti:5000 | xargs kill -9
   
   # Kill process on port 5173
   lsof -ti:5173 | xargs kill -9
   ```

2. **MongoDB connection issues**
   - Ensure MongoDB is running
   - Check connection string in `.env`
   - Verify network access (for Atlas)

3. **Node modules issues**
   ```bash
   # Clear npm cache
   npm cache clean --force
   
   # Delete node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📞 Support

If you encounter any issues, please check the troubleshooting section above or create an issue in the repository.
