import { Component } from 'react';
import axios from 'axios';

interface Car {
  _id: string;
  name: string;
  photos: string[];
  exShowroomPrice: number;
  features: string[];
}

interface State {
  cars: Car[];
  loading: boolean;
  error: string | null;
}

class CarsList extends Component<{}, State> {
  state: State = {
    cars: [],
    loading: true,
    error: null
  };

  componentDidMount() {
    axios.get('/api/cars')
      .then(response => {
        this.setState({ cars: response.data, loading: false });
      })
      .catch(error => {
        this.setState({ error: error.message, loading: false });
      });
  }

  render() {
    const { cars, loading, error } = this.state;

    if (loading) return <p>Loading cars...</p>;
    if (error) return <p>Error loading cars: {error}</p>;

    return (
      <div>
        <h1>Cars List</h1>
        <ul>
          {cars.map(car => (
            <li key={car._id}>
              <h2>{car.name}</h2>
              <p>Price: ₹{car.exShowroomPrice.toLocaleString()}</p>
              {car.photos.length > 0 && <img src={car.photos[0]} alt={car.name} style={{ maxWidth: '200px' }} />}
            </li>
          ))}
        </ul>
      </div>
    );
  }
}

export default CarsList;
