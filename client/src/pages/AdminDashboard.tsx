import React, { useState, useContext, useEffect } from 'react';
import axios from 'axios';
import { AuthContext } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const AdminDashboard: React.FC = () => {
  const { isAuthenticated, isAdmin } = useContext(AuthContext);
  const navigate = useNavigate();

  const [name, setName] = useState('');
  const [photos, setPhotos] = useState('');
  const [features, setFeatures] = useState('');
  const [exShowroomPrice, setExShowroomPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isAuthenticated || !isAdmin) {
      navigate('/login');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    setError('');

    try {
      const photosArray = photos.split(',').map(url => url.trim()).filter(Boolean);
      const featuresArray = features.split(',').map(f => f.trim()).filter(Boolean);
      const priceNum = parseFloat(exShowroomPrice);

      if (!name || photosArray.length === 0 || featuresArray.length === 0 || isNaN(priceNum)) {
        setError('Please fill in all fields with valid data.');
        setLoading(false);
        return;
      }

      await axios.post('/api/cars', {
        name,
        photos: photosArray,
        features: featuresArray,
        exShowroomPrice: priceNum,
      });

      setMessage('Car added successfully!');
      setName('');
      setPhotos('');
      setFeatures('');
      setExShowroomPrice('');
    } catch (err: any) {
      setError(err.response?.data?.error || err.message);
    }

    setLoading(false);
  };

  return (
    <div>
      <h1>Admin Dashboard - Add New Car</h1>
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}

      <form onSubmit={handleSubmit}>
        <div>
          <label>Car Name:</label><br />
          <input type="text" value={name} onChange={e => setName(e.target.value)} required />
        </div>
        <div>
          <label>Photos (comma separated URLs):</label><br />
          <textarea value={photos} onChange={e => setPhotos(e.target.value)} rows={3} required />
        </div>
        <div>
          <label>Features (comma separated):</label><br />
          <textarea value={features} onChange={e => setFeatures(e.target.value)} rows={3} required />
        </div>
        <div>
          <label>Ex-Showroom Price:</label><br />
          <input type="number" value={exShowroomPrice} onChange={e => setExShowroomPrice(e.target.value)} required />
        </div>
        <button type="submit" disabled={loading}>{loading ? 'Adding...' : 'Add Car'}</button>
      </form>
    </div>
  );
};

export default AdminDashboard;
